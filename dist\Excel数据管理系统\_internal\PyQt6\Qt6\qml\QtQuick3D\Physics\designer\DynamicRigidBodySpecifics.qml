// Copyright (C) 2023 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only

import QtQuick 2.15
import QtQuick.Layouts 1.15
import HelperWidgets 2.0

Column {
    width: parent.width

    DynamicRigidBodySection {
        width: parent.width
    }

    PhysicsBodySection {
        width: parent.width
    }

    PhysicsNodeSection {
        width: parent.width
    }

    NodeSection {
        width: parent.width
    }
}
