#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据管理系统最稳妥打包脚本
使用最稳妥的策略，确保所有功能都能正常使用
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_step(step, message):
    """打印步骤信息"""
    print(f"\n{'='*50}")
    print(f"🔧 步骤 {step}: {message}")
    print('='*50)

def clean_build_dirs():
    """清理之前的构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  ✅ 清理目录: {dir_name}")

def check_and_install_dependencies():
    """检查并安装所有必要的依赖"""
    print("📦 检查并安装必要的依赖...")

    # 必要的依赖列表
    dependencies = [
        ('PyInstaller', 'pyinstaller'),
        ('folium', 'folium'),
        ('PyQt6-WebEngine', 'PyQt6-WebEngine'),
    ]

    missing_deps = []

    # 检查PyInstaller
    try:
        import PyInstaller
        print("  ✅ PyInstaller已安装")
    except ImportError:
        missing_deps.append(('PyInstaller', 'pyinstaller'))
        print("  ❌ PyInstaller未安装")

    # 检查folium
    try:
        import folium
        print("  ✅ folium已安装")
    except ImportError:
        missing_deps.append(('folium', 'folium'))
        print("  ❌ folium未安装")

    # 检查PyQt6-WebEngine
    try:
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        print("  ✅ PyQt6-WebEngine已安装")
    except ImportError:
        missing_deps.append(('PyQt6-WebEngine', 'PyQt6-WebEngine'))
        print("  ❌ PyQt6-WebEngine未安装")

    # 安装缺失的依赖
    if missing_deps:
        print(f"\n📥 需要安装 {len(missing_deps)} 个依赖...")
        for name, package in missing_deps:
            try:
                print(f"  正在安装 {name}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', package],
                             check=True, capture_output=True, text=True)
                print(f"  ✅ {name} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ {name} 安装失败: {e}")
                return False

    print("✅ 所有依赖检查完成")
    return True

def install_pyinstaller():
    """安装PyInstaller（保留兼容性）"""
    return check_and_install_dependencies()

def build_stable_executable():
    """构建最稳妥的可执行文件"""
    print("🔨 开始构建最稳妥的可执行文件...")
    print("💡 策略：包含所有新增功能的依赖，确保功能100%完整")
    print("⚠️ 文件会比较大，但功能绝对完整")

    # 最稳妥的构建命令
    cmd_args = [
        sys.executable, '-m', 'PyInstaller',
        '--onedir',  # 使用目录模式
        '--windowed',  # 不显示控制台
        '--name', 'Excel数据管理系统',
        '--add-data', 'yunnan.json;.',
        '--add-data', 'work_orders.db;.',

        # 显式包含新增的关键模块
        '--hidden-import', 'folium',
        '--hidden-import', 'folium.plugins',
        '--hidden-import', 'folium.plugins.marker_cluster',
        '--hidden-import', 'folium.plugins.heat_map',
        '--hidden-import', 'folium.plugins.fullscreen',
        '--hidden-import', 'folium.plugins.measure_control',
        '--hidden-import', 'PyQt6.QtWebEngineWidgets',
        '--hidden-import', 'PyQt6.QtWebEngineCore',
        '--hidden-import', 'tempfile',
        '--hidden-import', 'webbrowser',
        '--hidden-import', 'json',
        '--hidden-import', 'collections.defaultdict',

        # 确保包含所有PyQt6模块
        '--hidden-import', 'PyQt6.QtCore',
        '--hidden-import', 'PyQt6.QtGui',
        '--hidden-import', 'PyQt6.QtWidgets',
        '--hidden-import', 'PyQt6.sip',

        # 确保包含matplotlib相关模块
        '--hidden-import', 'matplotlib.backends.backend_qt5agg',
        '--hidden-import', 'matplotlib.figure',
        '--hidden-import', 'matplotlib.patches',
        '--hidden-import', 'matplotlib.collections',
        '--hidden-import', 'matplotlib.dates',

        # 确保包含数据处理模块
        '--hidden-import', 'pandas',
        '--hidden-import', 'numpy',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'sqlite3',
        '--hidden-import', 'datetime',
        '--hidden-import', 'traceback',
        '--hidden-import', 'logging',

        # 只排除绝对不会用到的超大型库
        '--exclude-module', 'tensorflow',
        '--exclude-module', 'torch',
        '--exclude-module', 'torchvision',
        '--exclude-module', 'sklearn',
        '--exclude-module', 'cv2',
        '--exclude-module', 'IPython',
        '--exclude-module', 'jupyter',
        '--exclude-module', 'notebook',

        # 其他所有模块都保留，让PyInstaller自动处理
        '--clean',
        'main.py'
    ]
    
    print("📋 执行命令:")
    print("  pyinstaller --onedir --windowed [最少排除] main.py")
    
    try:
        print("⏳ 开始打包，这可能需要几分钟...")
        result = subprocess.run(cmd_args, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败!")
        print(f"错误代码: {e.returncode}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-1500:])
        return False

def test_all_functions():
    """测试所有功能是否正常"""
    print("🧪 创建功能测试脚本...")
    
    test_script = '''@echo off
chcp 65001
echo Excel数据管理系统功能测试
echo ================================
echo.
echo 正在启动程序进行功能测试...
echo.
echo 请测试以下功能：
echo 1. 程序是否正常启动
echo 2. 左侧导航栏是否显示正常
echo 3. 能否导入Excel文件
echo 4. 数据预览是否正常
echo 5. 归档可视化图表是否显示
echo 6. 静态地图功能是否正常
echo 7. 交互式地图功能是否正常（重要新功能）
echo 8. 在途数据处理是否正常
echo 9. 工单状态管理是否正确
echo 10. 数据导出功能是否可用
echo 11. 数据管理页面是否正常
echo.
echo 🗺️ 特别注意测试交互式地图功能：
echo   - 进入归档可视化 → 区域分析
echo   - 选择"交互式地图"
echo   - 检查是否能正常显示云南省地图
echo   - 测试地图缩放、拖拽、标记点击等功能
echo.
start "" "Excel数据管理系统.exe"
echo.
echo 程序已启动，请逐一测试上述功能
echo 如果所有功能都正常，说明打包成功！
echo.
pause
'''

    dist_dir = Path('dist/Excel数据管理系统')
    if dist_dir.exists():
        test_file = dist_dir / '功能测试.bat'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_script)
        print(f"  ✅ 创建功能测试脚本: {test_file}")

        # 手动复制数据文件
        print("📁 手动复制数据文件...")

        # 复制yunnan.json
        src_yunnan = Path('yunnan.json')
        dst_yunnan = dist_dir / 'yunnan.json'
        if src_yunnan.exists():
            shutil.copy2(src_yunnan, dst_yunnan)
            print(f"  ✅ 复制 yunnan.json")
        else:
            print(f"  ❌ 源文件 yunnan.json 不存在")

        # 复制work_orders.db
        src_db = Path('work_orders.db')
        dst_db = dist_dir / 'work_orders.db'
        if src_db.exists():
            shutil.copy2(src_db, dst_db)
            print(f"  ✅ 复制 work_orders.db")
        else:
            print(f"  ❌ 源文件 work_orders.db 不存在")

def analyze_and_optimize():
    """分析结果并提供优化建议"""
    print("📊 分析构建结果...")
    
    dist_dir = Path('dist/Excel数据管理系统')
    if not dist_dir.exists():
        print("❌ 构建目录不存在")
        return
    
    # 计算总大小
    total_size = 0
    file_count = 0
    
    for file_path in dist_dir.rglob('*'):
        if file_path.is_file():
            try:
                size = file_path.stat().st_size
                total_size += size
                file_count += 1
            except:
                pass
    
    total_size_mb = total_size / (1024 * 1024)
    total_size_gb = total_size_mb / 1024
    
    print(f"📁 构建结果分析:")
    print(f"  - 目录: {dist_dir}")
    print(f"  - 总大小: {total_size_mb:.2f} MB ({total_size_gb:.2f} GB)")
    print(f"  - 文件数量: {file_count}")
    
    # 检查主要文件
    exe_file = dist_dir / 'Excel数据管理系统.exe'
    if exe_file.exists():
        exe_size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"  - 主程序: {exe_size_mb:.2f} MB")
    
    # 检查关键文件
    key_files = ['yunnan.json', 'work_orders.db']
    for key_file in key_files:
        file_path = dist_dir / key_file
        if file_path.exists():
            print(f"  ✅ {key_file} 已包含")
        else:
            print(f"  ❌ {key_file} 缺失")
    
    # 提供优化建议
    print(f"\n💡 大小优化建议:")
    if total_size_gb > 1.0:
        print(f"  - 当前文件较大({total_size_gb:.2f}GB)，但功能完整")
        print(f"  - 如需缩小，可以考虑排除更多不用的库")
        print(f"  - 建议先测试功能是否完整，再考虑优化")
    else:
        print(f"  - 当前大小({total_size_mb:.0f}MB)可以接受")
        print(f"  - 功能应该完整可用")

def create_usage_guide():
    """创建详细使用指南"""
    print("📝 创建使用指南...")
    
    guide_text = '''# Excel数据管理系统 - 完整使用指南

## 🚀 启动和测试

### 第一步：功能测试
1. 双击 "功能测试.bat" 启动测试
2. 按照提示逐一测试各项功能
3. 确认所有功能都正常工作

### 第二步：正常使用
1. 双击 "Excel数据管理系统.exe" 启动程序
2. 首次启动可能需要30秒-1分钟

## 📋 功能清单

### 核心功能
- ✅ Excel文件导入和处理
- ✅ 数据预览和验证
- ✅ 数据库存储和管理

### 数据分析功能
- ✅ 归档可视化（图表分析）
- ✅ 区域分析（静态地图显示）
- ✅ 区域分析（交互式地图显示）🆕
- ✅ 月份变化分析
- ✅ 原因分析统计

### 数据管理功能
- ✅ 上传记录管理
- ✅ 数据导出（使用Excel模板）
- ✅ 数据删除和清理

### 专项功能
- ✅ 在途数据处理（已修复状态管理bug）🆕
- ✅ 工单流程选择（界面显示已优化）🆕
- ✅ 号码查询统计
- ✅ 测试时间修改

### 🆕 新增功能亮点
- 🗺️ **交互式云南省地图**：类似Google地图的缩放、拖拽体验
- 🔧 **工单状态管理优化**：修复了状态混乱bug，每个工单独立
- 📱 **界面显示优化**：修复了标题被挤压的显示问题
- 🌐 **Web引擎集成**：支持在应用内显示交互式地图
- 📍 **标记聚类**：自动聚合密集区域的工单标记
- 🔥 **热力图显示**：直观显示工单密度分布

## 🔧 版本特点

### 版本优势
- ✅ 包含所有必要的Python模块（包括folium、PyQt6-WebEngine）
- ✅ 不会出现模块缺失错误
- ✅ 所有功能都能正常使用（包括交互式地图）
- ✅ 稳定可靠，适合生产使用
- ✅ 支持最新的Web技术集成

### 注意事项
- ⚠️ 文件大小较大（可能超过1GB，因为包含Web引擎）
- ⚠️ 首次启动需要较长时间
- ⚠️ 需要复制整个文件夹，不能单独复制exe
- ⚠️ 交互式地图功能需要网络连接（加载地图瓦片）

## 📁 文件结构

```
Excel数据管理系统/
├── Excel数据管理系统.exe  # 主程序
├── _internal/                      # 依赖文件（勿删）
├── yunnan.json                     # 地图数据
├── work_orders.db                  # 数据库文件
├── 功能测试.bat                    # 功能测试脚本
└── 使用指南.txt                    # 本文件
```

## 🆘 故障排除

### 常见问题
Q: 程序启动很慢？
A: 正常现象，本版本包含完整依赖，首次启动需要时间

Q: 交互式地图不显示？
A: 1. 检查网络连接（需要加载地图瓦片）
   2. 如果WebEngine不可用，会自动使用浏览器打开
   3. 确保folium和PyQt6-WebEngine已正确打包

Q: 工单状态出现混乱？
A: 已修复状态管理bug，每个工单状态独立
   如仍有问题，请重启程序

Q: 流程选择标题看不到？
A: 已修复显示问题，标题应该清晰可见
   如仍有问题，请检查窗口大小

Q: 某个功能不工作？
A: 1. 检查是否有错误提示
   2. 尝试重启程序
   3. 检查数据文件是否完整

Q: 提示缺少文件？
A: 确保整个文件夹完整，不要删除任何文件

### 性能优化
- 建议在SSD硬盘上运行
- 确保有足够的内存（4GB+）
- 关闭不必要的后台程序

## 📞 技术支持

如有问题请联系开发团队。

---
版本: 完整版
特点: 功能完整，稳定可靠
打包时间: {build_time}
'''
    
    from datetime import datetime
    build_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    dist_dir = Path('dist/Excel数据管理系统')
    if dist_dir.exists():
        guide_file = dist_dir / '使用指南.txt'
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_text.format(build_time=build_time))
        print(f"  ✅ 创建使用指南: {guide_file}")

def main():
    """主函数"""
    print("🚀 Excel数据管理系统最稳妥打包工具")
    print("=" * 60)
    print("🎯 目标：确保所有功能100%可用（包括新增的交互式地图）")
    print("💡 策略：包含所有依赖模块（folium、PyQt6-WebEngine等）")
    print("⚠️ 结果：文件较大但功能完整")
    print("🆕 新功能：交互式地图、工单状态管理优化、界面显示修复")
    print("=" * 60)
    
    # 检查主文件
    if not os.path.exists('main.py'):
        print("❌ 找不到main.py文件，请在项目根目录运行此脚本")
        return
    
    # 步骤1: 清理构建目录
    print_step(1, "清理构建环境")
    clean_build_dirs()
    
    # 步骤2: 安装PyInstaller
    print_step(2, "安装PyInstaller")
    if not install_pyinstaller():
        print("❌ 无法安装PyInstaller，打包终止")
        return
    
    # 步骤3: 构建可执行文件
    print_step(3, "构建可执行文件")
    if not build_stable_executable():
        print("❌ 构建失败，打包终止")
        return
    
    # 步骤4: 分析结果
    print_step(4, "分析构建结果")
    analyze_and_optimize()
    
    # 步骤5: 创建测试脚本
    print_step(5, "创建功能测试脚本")
    test_all_functions()
    
    # 步骤6: 创建使用指南
    print_step(6, "创建使用指南")
    create_usage_guide()
    
    # 完成
    print("\n" + "🎉" * 20)
    print("🎉 打包完成!")
    print("🎉" * 20)

    print(f"\n📁 程序位置:")
    print(f"   dist/Excel数据管理系统/Excel数据管理系统.exe")

    print(f"\n🧪 重要：请先进行功能测试!")
    print(f"   1. 进入 dist/Excel数据管理系统/ 目录")
    print(f"   2. 双击 '功能测试.bat' 进行测试")
    print(f"   3. 确认所有功能都正常工作")

    print(f"\n💡 版本特点:")
    print(f"   ✅ 功能100%完整（包括交互式地图）")
    print(f"   ✅ 不会缺少任何模块（folium、PyQt6-WebEngine等）")
    print(f"   ✅ 稳定可靠")
    print(f"   ✅ 支持最新Web技术")
    print(f"   ⚠️ 文件较大（因为包含Web引擎）")

    print(f"\n📋 下一步:")
    print(f"   1. 测试所有功能（特别是交互式地图）")
    print(f"   2. 验证工单状态管理是否正常")
    print(f"   3. 检查界面显示是否正确")
    print(f"   4. 如果功能完整，可以正常使用")
    print(f"   5. 如需缩小文件，再考虑优化")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
