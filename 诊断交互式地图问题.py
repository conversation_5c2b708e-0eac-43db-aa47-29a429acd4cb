# -*- coding: utf-8 -*-
"""
诊断交互式地图问题
"""
import sys
import os

def check_folium_installation():
    """检查folium安装情况"""
    print("🔍 检查folium安装情况...")
    
    try:
        import folium
        print(f"✅ folium已安装，版本: {folium.__version__}")
        return True
    except ImportError as e:
        print(f"❌ folium未安装: {e}")
        return False

def test_simple_map():
    """测试简单地图创建"""
    print("\n🗺️ 测试简单地图创建...")
    
    try:
        import folium
        import tempfile
        import webbrowser
        
        # 创建最简单的地图
        m = folium.Map(location=[25.0, 102.0], zoom_start=7)
        
        # 添加一个标记
        folium.Marker(
            [25.0, 102.0], 
            popup="测试标记",
            tooltip="点击查看"
        ).add_to(m)
        
        # 保存到临时文件
        temp_file = os.path.join(tempfile.gettempdir(), 'simple_test_map.html')
        m.save(temp_file)
        
        print(f"✅ 地图已保存到: {temp_file}")
        
        # 检查文件内容
        with open(temp_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 检查关键内容
        if 'leaflet' in content.lower():
            print("✅ 包含Leaflet库引用")
        else:
            print("❌ 未找到Leaflet库引用")
            
        if 'var map_' in content:
            print("✅ 包含地图变量定义")
        else:
            print("❌ 未找到地图变量定义")
            
        # 查找可能的错误源
        lines = content.split('\n')
        script_lines = [line for line in lines if '<script' in line.lower()]
        
        print(f"\n📜 发现 {len(script_lines)} 个脚本标签:")
        for i, line in enumerate(script_lines[:5]):  # 只显示前5个
            print(f"  {i+1}. {line.strip()[:80]}...")
            
        # 在浏览器中打开测试
        print(f"\n🌐 在浏览器中打开测试地图...")
        webbrowser.open(f'file://{temp_file}')
        
        return True, temp_file
        
    except Exception as e:
        print(f"❌ 地图创建失败: {e}")
        return False, None

def test_webengine():
    """测试WebEngine"""
    print("\n🧪 测试PyQt6-WebEngine...")
    
    try:
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        from PyQt6.QtCore import QUrl
        print("✅ PyQt6-WebEngine导入成功")
        return True
    except ImportError as e:
        print(f"❌ PyQt6-WebEngine导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ PyQt6-WebEngine加载失败: {e}")
        return False

def diagnose_js_error():
    """诊断JavaScript错误"""
    print("\n🔍 诊断JavaScript错误...")
    
    try:
        import folium
        import tempfile
        
        # 创建地图并检查生成的HTML
        m = folium.Map(location=[25.0, 102.0], zoom_start=7)
        
        # 添加一些插件来测试
        try:
            from folium.plugins import MarkerCluster, HeatMap
            
            # 添加标记聚类
            marker_cluster = MarkerCluster().add_to(m)
            folium.Marker([25.0, 102.0]).add_to(marker_cluster)
            
            # 添加热力图
            HeatMap([[25.0, 102.0]]).add_to(m)
            
            print("✅ 插件添加成功")
            
        except Exception as e:
            print(f"⚠️ 插件添加失败: {e}")
        
        # 保存并检查
        temp_file = os.path.join(tempfile.gettempdir(), 'diagnosis_map.html')
        m.save(temp_file)
        
        with open(temp_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查可能导致"L is not defined"错误的原因
        issues = []
        
        # 1. 检查Leaflet库是否正确加载
        if 'leaflet.js' not in content.lower():
            issues.append("未找到leaflet.js库引用")
            
        # 2. 检查脚本加载顺序
        script_order = []
        lines = content.split('\n')
        for line in lines:
            if '<script' in line.lower() and 'src=' in line.lower():
                if 'leaflet' in line.lower():
                    script_order.append('leaflet')
                elif 'folium' in line.lower():
                    script_order.append('folium')
                    
        if script_order and script_order[0] != 'leaflet':
            issues.append("Leaflet库可能未在其他脚本之前加载")
            
        # 3. 检查CDN链接
        import re
        cdn_links = re.findall(r'https?://[^"\'>\s]+', content)
        broken_links = []
        
        print(f"\n📡 发现 {len(cdn_links)} 个外部链接")
        for link in cdn_links[:3]:  # 只检查前3个
            print(f"  - {link}")
            
        if issues:
            print(f"\n⚠️ 发现 {len(issues)} 个潜在问题:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("\n✅ 未发现明显的结构问题")
            
        return temp_file
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始诊断交互式地图问题...")
    print("=" * 50)
    
    # 1. 检查folium安装
    folium_ok = check_folium_installation()
    
    if not folium_ok:
        print("\n💡 解决方案: 安装folium")
        print("pip install folium")
        return
    
    # 2. 测试简单地图
    map_ok, map_file = test_simple_map()
    
    # 3. 测试WebEngine
    webengine_ok = test_webengine()
    
    # 4. 诊断JS错误
    if map_ok:
        diagnosis_file = diagnose_js_error()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    print(f"  Folium安装: {'✅' if folium_ok else '❌'}")
    print(f"  地图创建: {'✅' if map_ok else '❌'}")
    print(f"  WebEngine: {'✅' if webengine_ok else '❌'}")
    
    if folium_ok and map_ok:
        print("\n💡 建议:")
        if not webengine_ok:
            print("  - PyQt6-WebEngine有问题，但地图可以在浏览器中正常显示")
            print("  - 这是常见的Windows兼容性问题")
        print("  - 尝试在浏览器中查看生成的地图文件")
        print("  - 检查浏览器控制台是否有JavaScript错误")
        
        if map_file:
            print(f"\n📁 测试文件位置: {map_file}")
            
    print("\n🔧 如果问题仍然存在，请:")
    print("  1. 更新folium到最新版本: pip install --upgrade folium")
    print("  2. 检查网络连接（地图需要加载在线资源）")
    print("  3. 尝试使用不同的浏览器")

if __name__ == "__main__":
    main()
