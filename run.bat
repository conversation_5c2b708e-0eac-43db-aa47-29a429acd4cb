@echo off
title Excel Data Management System

echo ================================================================
echo                Excel Data Management System
echo                      Quick Start Tool
echo ================================================================
echo.

echo Checking Python...

python --version >nul 2>&1
if %errorLevel% == 0 (
    echo Python is installed
    python --version
    goto :install_deps
) else (
    echo Python not found, downloading Python 3.10.2...
    goto :install_python
)

:install_python
echo.
echo ================================================================
echo                 Installing Python 3.10.2
echo ================================================================
echo.

:: Create temp directory
if not exist "temp" mkdir temp

echo Downloading Python 3.10.2 installer...
echo This may take a few minutes, please wait...

:: Download Python 3.10.2
powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.10.2/python-3.10.2-amd64.exe' -OutFile 'temp\python310-installer.exe'}"

if exist "temp\python310-installer.exe" (
    echo Python installer downloaded successfully
    echo.
    echo Installing Python 3.10.2...
    echo - Adding to system PATH
    echo - Installing pip package manager
    echo - Installing standard libraries
    echo.

    :: Silent install with PATH addition
    temp\python310-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 Include_doc=0 AssociateFiles=1

    echo Python 3.10.2 installation completed
    echo.

    :: Wait for installation to complete
    timeout /t 5 /nobreak >nul

    :: Refresh environment variables
    echo Refreshing environment variables...
    call :refresh_env

    :: Verify installation
    python --version >nul 2>&1
    if %errorLevel% == 0 (
        echo Python installation verified successfully
        python --version
    ) else (
        echo Python installation may have failed
        echo Please restart your computer and try again
        echo Or manually install Python 3.10.2 from: https://www.python.org/downloads/
        pause
        exit /b 1
    )
) else (
    echo Python download failed
    echo Please check your internet connection
    echo Or manually download Python 3.10.2 from: https://www.python.org/downloads/
    pause
    exit /b 1
)

:install_deps

echo.
echo Checking and installing dependencies...

echo Upgrading pip...
python -m pip install --upgrade pip --quiet

echo Checking PyQt6...
python -c "import PyQt6" >nul 2>&1
if %errorLevel% == 0 (
    echo PyQt6 already installed
) else (
    echo Installing PyQt6...
    python -m pip install PyQt6>=6.4.0 --quiet
)

echo Checking PyQt6-WebEngine...
python -c "import PyQt6.QtWebEngineWidgets" >nul 2>&1
if %errorLevel% == 0 (
    echo PyQt6-WebEngine already installed
) else (
    echo Installing PyQt6-WebEngine...
    python -m pip install PyQt6-WebEngine>=6.4.0 --quiet
)

echo Checking openpyxl...
python -c "import openpyxl" >nul 2>&1
if %errorLevel% == 0 (
    echo openpyxl already installed
) else (
    echo Installing openpyxl...
    python -m pip install openpyxl --quiet
)

echo Checking matplotlib...
python -c "import matplotlib" >nul 2>&1
if %errorLevel% == 0 (
    echo matplotlib already installed
) else (
    echo Installing matplotlib...
    python -m pip install matplotlib --quiet
)

echo Checking folium...
python -c "import folium" >nul 2>&1
if %errorLevel% == 0 (
    echo folium already installed
) else (
    echo Installing folium (compatible version)...
    python -m pip install folium>=0.14.0 --quiet
)

echo Checking pandas...
python -c "import pandas" >nul 2>&1
if %errorLevel% == 0 (
    echo pandas already installed
) else (
    echo Installing pandas...
    python -m pip install pandas --quiet
)

echo Checking branca...
python -c "import branca" >nul 2>&1
if %errorLevel% == 0 (
    echo branca already installed
) else (
    echo Installing branca...
    python -m pip install branca>=0.6.0 --quiet
)

echo Checking jinja2...
python -c "import jinja2" >nul 2>&1
if %errorLevel% == 0 (
    echo jinja2 already installed
) else (
    echo Installing jinja2...
    python -m pip install jinja2>=3.0.0 --quiet
)

echo.
echo Dependencies check completed!

echo.
echo Checking program files...

if not exist "main.py" (
    echo main.py not found
    echo Please make sure this file is in the same directory as main.py
    pause
    exit /b 1
) else (
    echo main.py found
)

if not exist "work_orders.db" (
    echo work_orders.db not found, will create new database
) else (
    echo work_orders.db found
)

if not exist "yunnan.json" (
    echo yunnan.json not found, regional analysis may be limited
) else (
    echo yunnan.json found
)

echo.
echo Starting Excel Data Management System...
echo.

set PYTHONIOENCODING=utf-8
set PYTHONPATH=%cd%

python main.py

echo.
echo Program exited
echo.
goto :cleanup

:refresh_env
:: Refresh environment variables without restart
echo Updating PATH environment variable...
for /f "skip=2 tokens=3*" %%a in ('reg query HKLM\SYSTEM\CurrentControlSet\Control\Session" Manager\Environment" /v PATH') do set "SysPath=%%a %%b"
for /f "skip=2 tokens=3*" %%a in ('reg query HKCU\Environment /v PATH 2^>nul') do set "UsrPath=%%a %%b"
set "PATH=%SysPath%;%UsrPath%"
goto :eof

:cleanup
:: Clean up temporary files
if exist "temp" (
    echo Cleaning up temporary files...
    rmdir /s /q temp >nul 2>&1
)

pause
exit /b 0