Metadata-Version: 2.4
Name: xyzservices
Version: 2025.4.0
Summary: Source of XYZ tiles providers
Home-page: https://github.com/geopandas/xyzservices
Author: <PERSON>, <PERSON>
Author-email: <EMAIL>, martin@martin<PERSON><PERSON>mann.net
License: 3-Clause BSD
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: requires-python
Dynamic: summary

# xyzservices - Source of XYZ tiles providers

`xyzservices` is a lightweight library providing a repository of available XYZ services
offering raster basemap tiles. The repository is provided via Python API and as a
compressed JSON file.

XYZ tiles can be used as background for your maps to provide necessary spatial context.
`xyzservices` offer specifications of many tile services and provide an easy-to-use
tools to plug them into your work, no matter if interactive or static.

[![Tests](https://github.com/geopandas/xyzservices/actions/workflows/tests.yaml/badge.svg)](https://github.com/geopandas/xyzservices/actions/workflows/tests.yaml) [![codecov](https://codecov.io/gh/geopandas/xyzservices/branch/main/graph/badge.svg?token=PBSZQA48GY)](https://codecov.io/gh/geopandas/xyzservices) [![PyPi](https://img.shields.io/pypi/v/xyzservices.svg)](https://pypi.python.org/pypi/xyzservices)

## Quick Start

Using `xyzservices` is simple and in most cases does not involve more than a line of
code.

### Installation

You can install `xyzservices` from `conda` or `pip`:

```shell
conda install xyzservices -c conda-forge
```

```shell
pip install xyzservices
```

The package does not depend on any other apart from those built-in in Python.

### Providers API

The key part of `xyzservices` are providers:

```py
>>> import xyzservices.providers as xyz
```

`xyzservices.providers` or just `xyz` for short is a `Bunch` of providers, an enhanced
`dict`. If you are in Jupyter-like environment, `xyz` will offer collapsible inventory
of available XYZ tile sources. You can also explore it as a standard `dict` using
`xyz.keys()`. Once you have picked your provider, you get its details as a
`TileProvider` object with all the details you may need:

```py
>>> xyz.CartoDB.Positron.url
'https://{s}.basemaps.cartocdn.com/{variant}/{z}/{x}/{y}{r}.png'

>>> xyz.CartoDB.Positron.attribution
'(C) OpenStreetMap contributors (C) CARTO'
```

You can also check if the `TileProvider` needs API token and pass it to the object if
needed.

```py
>>> xyz.MapBox.requires_token()
True

>>> xyz.MapBox["accessToken"] = "my_personal_token"
>>> xyz.MapBox.requires_token()
False
```

### Providers JSON

After the installation, you will find the JSON used as a database of providers in
`share/xyzservices/providers.json` if you want to use it outside of a Python ecosystem.

## Contributors

`xyzservices` is developed by a community of enthusiastic volunteers and lives under
[`geopandas`](https://github.com/geopandas) GitHub organization. You can see a full list
of contributors [here](https://github.com/geopandas/xyzservices/graphs/contributors).

The main group of providers is retrieved from the [`leaflet-providers`
project](https://github.com/leaflet-extras/leaflet-providers) that contains both openly
accessible providers as well as those requiring registration. All of them are considered
[free](https://github.com/leaflet-extras/leaflet-providers/blob/master/README.md#what-do-we-mean-by-free).

If you would like to contribute to the project, have a look at the list of
[open issues](https://github.com/geopandas/contextily/issues), particularly those labeled as
[good first issue](https://github.com/geopandas/xyzservices/issues?q=is%3Aopen+is%3Aissue+label%3A%22good+first+issue%22).

## License

BSD 3-Clause License

Resources coming from the [`leaflet-providers`
project](https://github.com/leaflet-extras/leaflet-providers) are licensed under BSD
2-Clause License (© 2013 Leaflet Providers)
