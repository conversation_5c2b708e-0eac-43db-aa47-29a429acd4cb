# -*- coding: utf-8 -*-
"""
打包环境工单状态bug修复脚本
专门解决打包后软件中工单状态混乱的问题
"""

import sys
import os
import traceback
from datetime import datetime

def create_debug_patch():
    """创建调试补丁，帮助诊断打包环境中的问题"""
    
    patch_code = '''
# 打包环境工单状态bug修复补丁
# 在main.py的show_transit_detail_page方法开头添加以下代码

def show_transit_detail_page_debug_patch(self, item):
    """调试版本的show_transit_detail_page方法"""
    
    # 创建调试日志文件
    debug_log_path = os.path.join(os.path.dirname(__file__), 'work_order_debug.log')
    
    def debug_log(message):
        """写入调试日志"""
        try:
            with open(debug_log_path, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"[{timestamp}] {message}\\n")
        except:
            pass
    
    debug_log("=== 开始处理工单切换 ===")
    
    if not item:
        debug_log("ERROR: item为空")
        return

    # 获取选中行的数据
    row = item.row()
    row_data = []
    for col in range(self.transit_table.columnCount()):
        cell_item = self.transit_table.item(row, col)
        row_data.append(cell_item.text() if cell_item else "")
    
    debug_log(f"选中行数据: {row_data[:3]}...")  # 只记录前3列避免日志过长

    # 获取新的工单ID
    try:
        excel_row_number = int(row_data[0])
        new_work_order_id = self.get_work_order_id_by_row(excel_row_number)
        debug_log(f"新工单ID: {new_work_order_id}, Excel行号: {excel_row_number}")

        # 获取当前工单ID
        current_id = getattr(self, 'current_work_order_id', None)
        debug_log(f"当前工单ID: {current_id}")
        
        # 强制清理状态的标志
        force_clear = False
        
        # 检查是否是同一个工单
        if (current_id is not None and 
            new_work_order_id is not None and 
            str(current_id) == str(new_work_order_id)):
            debug_log("✅ 同一个工单，保持状态")
        else:
            debug_log(f"🔄 不同工单，需要清理状态 (从 {current_id} 到 {new_work_order_id})")
            force_clear = True
        
        # 如果需要清理状态
        if force_clear:
            debug_log("开始强制清理状态...")
            
            # 方法1: 清理复选框
            try:
                if hasattr(self, 'process_checkboxes') and self.process_checkboxes:
                    debug_log(f"找到 {len(self.process_checkboxes)} 个复选框")
                    for key, checkbox in self.process_checkboxes.items():
                        if checkbox and hasattr(checkbox, 'setChecked'):
                            was_checked = checkbox.isChecked()
                            checkbox.setChecked(False)
                            debug_log(f"  复选框 {key}: {was_checked} -> False")
                        else:
                            debug_log(f"  复选框 {key}: 无效对象")
                else:
                    debug_log("未找到process_checkboxes或为空")
            except Exception as e:
                debug_log(f"清理复选框失败: {e}")
            
            # 方法2: 清理下拉框
            try:
                if hasattr(self, 'process_reason_combos') and self.process_reason_combos:
                    debug_log(f"找到 {len(self.process_reason_combos)} 个下拉框")
                    for key, combo in self.process_reason_combos.items():
                        if combo and hasattr(combo, 'setCurrentIndex'):
                            old_index = combo.currentIndex()
                            combo.setCurrentIndex(0)
                            debug_log(f"  下拉框 {key}: {old_index} -> 0")
                        else:
                            debug_log(f"  下拉框 {key}: 无效对象")
                else:
                    debug_log("未找到process_reason_combos或为空")
            except Exception as e:
                debug_log(f"清理下拉框失败: {e}")
            
            # 方法3: 清理内存属性
            state_attrs = [
                'selected_processes',
                'selected_process_details', 
                'current_process_index',
                'process_results',
                'current_page_selections',
                'pending_page_selections'
            ]
            
            for attr_name in state_attrs:
                try:
                    if hasattr(self, attr_name):
                        old_value = getattr(self, attr_name, None)
                        delattr(self, attr_name)
                        debug_log(f"  删除属性 {attr_name}: {type(old_value)}")
                    else:
                        debug_log(f"  属性 {attr_name}: 不存在")
                except Exception as e:
                    debug_log(f"  删除属性 {attr_name} 失败: {e}")
            
            # 方法4: 强制重置
            try:
                self.selected_processes = []
                self.selected_process_details = []
                self.current_process_index = 0
                self.process_results = {}
                self.current_page_selections = {}
                self.pending_page_selections = {}
                debug_log("强制重置状态变量完成")
            except Exception as e:
                debug_log(f"强制重置失败: {e}")
        
        # 设置新的工单ID
        self.current_work_order_id = new_work_order_id
        self.current_work_order_data = row_data
        debug_log(f"设置新工单ID: {new_work_order_id}")
        
        # 尝试恢复新工单的流程进度
        try:
            self.restore_process_progress()
            debug_log("恢复流程进度完成")
        except Exception as e:
            debug_log(f"恢复流程进度失败: {e}")

    except Exception as e:
        debug_log(f"处理工单ID失败: {e}")
        debug_log(f"异常详情: {traceback.format_exc()}")
        # 紧急清理
        try:
            if hasattr(self, 'process_checkboxes'):
                for cb in self.process_checkboxes.values():
                    if cb: cb.setChecked(False)
            if hasattr(self, 'process_reason_combos'):
                for combo in self.process_reason_combos.values():
                    if combo: combo.setCurrentIndex(0)
            debug_log("执行了紧急清理")
        except:
            debug_log("紧急清理也失败了")
        
        self.current_work_order_id = None
        self.current_work_order_data = row_data

    # 切换到详细操作页面
    self.stacked_widget.setCurrentWidget(self.transit_detail_page)
    
    # 更新详细信息显示
    self.update_transit_detail_info(row_data)
    
    debug_log("=== 工单切换处理完成 ===\\n")
'''
    
    return patch_code

def create_verification_script():
    """创建验证脚本"""
    
    script_content = '''
# 工单状态验证脚本
# 在打包后的软件目录中运行此脚本来验证修复效果

import os
import sys
from datetime import datetime

def check_debug_log():
    """检查调试日志"""
    log_path = 'work_order_debug.log'
    
    if not os.path.exists(log_path):
        print("❌ 调试日志文件不存在")
        print("请先运行软件并切换几个工单，然后再检查")
        return False
    
    print("📋 调试日志内容:")
    print("=" * 50)
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 显示最近的日志
        recent_lines = lines[-50:] if len(lines) > 50 else lines
        
        for line in recent_lines:
            print(line.strip())
            
        print("=" * 50)
        print(f"总共 {len(lines)} 行日志")
        
        # 分析日志
        clear_count = sum(1 for line in lines if "开始强制清理状态" in line)
        error_count = sum(1 for line in lines if "失败" in line or "ERROR" in line)
        
        print(f"\\n📊 日志分析:")
        print(f"  清理操作次数: {clear_count}")
        print(f"  错误次数: {error_count}")
        
        if error_count == 0:
            print("✅ 没有发现错误")
        else:
            print("⚠️ 发现错误，请检查日志详情")
            
        return True
        
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 工单状态bug验证工具")
    print("=" * 40)
    check_debug_log()
'''
    
    return script_content

def main():
    """主函数"""
    print("🔧 打包环境工单状态bug修复工具")
    print("=" * 50)
    
    print("📝 问题描述:")
    print("  在打包后的软件中，切换工单时之前的选择状态没有被清理")
    print("  导致新工单显示了上一个工单的选择状态")
    
    print("\\n🎯 解决方案:")
    print("  1. 强化状态清理逻辑，增加多重保险")
    print("  2. 添加详细的调试日志")
    print("  3. 提供验证工具")
    
    print("\\n📋 修复步骤:")
    print("  1. 已在main.py中强化了clear_previous_work_order_state方法")
    print("  2. 已在show_transit_detail_page中增加了多重清理保险")
    print("  3. 重新打包软件")
    print("  4. 测试修复效果")
    
    # 创建调试补丁文件
    patch_code = create_debug_patch()
    with open('工单状态调试补丁.py', 'w', encoding='utf-8') as f:
        f.write(patch_code)
    print("\\n✅ 已创建调试补丁文件: 工单状态调试补丁.py")
    
    # 创建验证脚本
    verification_script = create_verification_script()
    with open('验证工单状态修复.py', 'w', encoding='utf-8') as f:
        f.write(verification_script)
    print("✅ 已创建验证脚本: 验证工单状态修复.py")
    
    print("\\n🚀 下一步操作:")
    print("  1. 重新运行打包脚本: python build_optimized.py")
    print("  2. 测试打包后的软件")
    print("  3. 如果问题仍然存在，在软件目录中运行: python 验证工单状态修复.py")
    print("  4. 查看调试日志了解具体问题")

if __name__ == "__main__":
    main()
